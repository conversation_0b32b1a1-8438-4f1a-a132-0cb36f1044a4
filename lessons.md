# Lessons Learned

## Build Issues

### Issue: Bun Runtime Not Found (2025-01-14)

**Problem**: Build process failed with error "The system cannot find the path specified" when trying to run `bun run scripts/prepare-bundle-native.js`.

**Root Cause**: Bun runtime was not installed on the system. The build scripts require Bun for:
- Running the bundle preparation script
- Compiling executables with native embedding features
- Handling embedded assets (yoga.wasm, ripgrep binaries)

**Solution**: 
1. Install Bun using Windows Package Manager:
   ```powershell
   winget install --id Oven-sh.Bun.BaselineProfile
   ```

2. Update PATH environment variable or restart shell to make `bun` command available:

   ```powershell
   $env:PATH += ";$env:USERPROFILE\.bun\bin"
   ```
3. Verify installation:

   ```powershell
   bun --version
   ```

**Prevention**: 
- Add Bun installation check to build scripts
- Document Bun as a required dependency in README
- Consider adding a setup script that checks for all required tools

**Files Affected**:
- `scripts/build-executables.js` - Main build script
- `scripts/prepare-bundle-native.js` - Bundle preparation script
- `scripts/fetch-and-build.js` - Package fetching and build orchestration

**Build Output**: Successfully created `claude-code-x86_64-pc-windows-msvc.exe` in `src-tauri/binaries/` directory.

## Dependencies

### Required Tools

- **Node.js**: v22.14.0+ (for running build scripts)
- **Bun**: v1.2.18+ (for executable compilation and native embedding)
- **npm**: For package management and downloading Claude Code packages

### Build Process

1. Fetch Claude Code package from npm
2. Extract and copy required files (cli.js, yoga.wasm, vendor/)
3. Prepare bundle with native Bun embedding
4. Compile executable with embedded assets
5. Clean up temporary files

## Notes

- Build process includes embedded assets for offline operation
- Executables follow Tauri sidecar triple naming convention
- Multiple variants available (baseline, modern) for different CPU architectures
- All executables are optimized with minification and sourcemaps